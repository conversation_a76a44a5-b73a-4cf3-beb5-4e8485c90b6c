<!DOCTYPE html>
<html lang="En">

<head>
    <title><PERSON>'s Portfolio</title>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles/main.css">
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loader">
            <div class="loader-text">
                <span>R</span><span>Y</span><span>A</span><span>N</span>
                <span class="space"></span>
                <span>H</span><span>A</span><span>U</span><span>G</span>
            </div>
            <div class="loader-bar">
                <div class="loader-progress"></div>
            </div>
        </div>
    </div>

    <!-- Animated Background -->
    <div id="particles-background"></div>

    <!-- Theme Toggle -->
    <button id="theme-toggle" aria-label="Toggle theme">
        <span class="sun-icon">☀️</span>
        <span class="moon-icon">🌙</span>
    </button>

    <Header>
        <nav>
            <a href="#About">About Me</a>
            <a href="#Projects">Projects</a>
            <a href="#Experience">Experience</a>
        </nav>
        <h1>Ryan Haug</h1>
        <nav>
            <a href="#Certifications">Certifications</a>
            <a href="#Contact">Contact Me</a>
        </nav>

        <div class="Line-Divider"></div>
    </Header>

    <main>
        <section id="Home">
            <Div id="Home-Image">
                <img src="" alt="photo of self">
            </Div>
            <Div id="Home-Content">
                <h2 id="typing-text">Software Engineer</h2>

                <div class="Line-Divider"></div>

                <p>Passionate about creating innovative solutions through code. I specialize in full-stack development,
                game development, and emerging technologies like VR/AR. With a strong foundation in multiple programming
                languages and modern frameworks, I bring ideas to life through clean, efficient, and scalable code.
                </p>

                <!-- Social Links -->
                <div class="social-links">
                    <a href="#" aria-label="LinkedIn" class="social-link">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <a href="#" aria-label="GitHub" class="social-link">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                    </a>
                    <a href="#" aria-label="Email" class="social-link">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                    </a>
                </div>
            </Div>
            <div id="Home-Scroll">
                <p>Please Scroll! (placeholder)</p>
            </div>
        </section>

        <div class="Line-Divider"></div>

        <section id="About" class="Main-Content">
            <h1>About Me</h1>

            <div class="About-Photo">
                <img src="" alt="Professional headshot of Ryan">
            </div>
            <div class="About-Text">
                <p>Welcome to my digital portfolio! I'm a passionate software engineer with a love for creating
                innovative solutions that make a difference. My journey in technology began with curiosity and has
                evolved into a career focused on building robust, scalable applications and immersive experiences.
                </p>
            </div>
            <div class="About-Text">
                <p>When I'm not coding, you'll find me exploring the latest tech trends, contributing to open-source
                projects, or enjoying outdoor adventures. I believe in continuous learning and am always excited to
                take on new challenges that push the boundaries of what's possible with technology.
                </p>
            </div>
            <div class="About-Photo">
                <img src="" alt="Ryan enjoying outdoor activities">
            </div>
        </section>

        <div class="Line-Divider"></div>

        <section id="Projects" class="Main-Content">
            <h1>Featured Projects</h1>

            <div class="projects-grid">
                <div class="project-card" data-aos="fade-up">
                    <div class="project-image">
                        <img src="" alt="E-Commerce Platform Screenshot">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">Live Demo</a>
                                <a href="#" class="project-link">GitHub</a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>E-Commerce Platform</h3>
                        <p>A full-stack e-commerce solution built with modern web technologies. Features include user authentication,
                        payment processing, inventory management, and responsive design.</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                            <span class="tech-tag">Stripe API</span>
                        </div>
                    </div>
                </div>

                <div class="project-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="project-image">
                        <img src="" alt="VR Game Environment Screenshot">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">Play Demo</a>
                                <a href="#" class="project-link">GitHub</a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>VR Adventure Game</h3>
                        <p>An immersive virtual reality adventure game featuring realistic physics, interactive environments,
                        and engaging gameplay mechanics. Optimized for multiple VR platforms.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Unity</span>
                            <span class="tech-tag">C#</span>
                            <span class="tech-tag">VR SDK</span>
                            <span class="tech-tag">3D Modeling</span>
                        </div>
                    </div>
                </div>

                <div class="project-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="project-image">
                        <img src="" alt="Mobile App Interface Screenshot">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">App Store</a>
                                <a href="#" class="project-link">GitHub</a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>Task Management App</h3>
                        <p>A cross-platform mobile application for productivity and task management. Features real-time
                        synchronization, offline support, and intuitive user interface design.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Kotlin</span>
                            <span class="tech-tag">Android Studio</span>
                            <span class="tech-tag">Firebase</span>
                            <span class="tech-tag">Material Design</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="Experience" class="Main-Content">
            <h1>My Experience</h1>

            <div class="experience-container">
                <div class="skills-section">
                    <h3>Programming Languages</h3>
                    <div class="skills-grid">
                        <div class="skill-item" data-aos="fade-right">
                            <div class="skill-info">
                                <span class="skill-name">JavaScript</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>
                        <div class="skill-item" data-aos="fade-right" data-aos-delay="100">
                            <div class="skill-info">
                                <span class="skill-name">C#</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>
                        <div class="skill-item" data-aos="fade-right" data-aos-delay="200">
                            <div class="skill-info">
                                <span class="skill-name">HTML/CSS</span>
                                <span class="skill-percentage">95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95"></div>
                            </div>
                        </div>
                        <div class="skill-item" data-aos="fade-right" data-aos-delay="300">
                            <div class="skill-info">
                                <span class="skill-name">Kotlin</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tools-section">
                    <h3>Development Tools</h3>
                    <div id="Experience-Tools">
                        <div class="tool-item" data-aos="zoom-in">
                            <img src="" alt="Visual Studio logo">
                            <span class="tool-name">Visual Studio</span>
                        </div>
                        <div class="tool-item" data-aos="zoom-in" data-aos-delay="100">
                            <img src="" alt="Android Studio logo">
                            <span class="tool-name">Android Studio</span>
                        </div>
                        <div class="tool-item" data-aos="zoom-in" data-aos-delay="200">
                            <img src="" alt="Unity logo">
                            <span class="tool-name">Unity</span>
                        </div>
                        <div class="tool-item" data-aos="zoom-in" data-aos-delay="300">
                            <img src="" alt="GitHub logo">
                            <span class="tool-name">GitHub</span>
                        </div>
                        <div class="tool-item" data-aos="zoom-in" data-aos-delay="400">
                            <img src="" alt="Azure logo">
                            <span class="tool-name">Azure</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="Certifications" class="Main-Content">
            <h1>Certifications</h1>

            <div id="Certification-Selector">
                <ul>
                    <li>CompTIA ITF</li>
                    <li>Pearson HTML and CSS</li>
                    <li>Unity Certified Programmer</li>
                    <li>Unity Certified VR Developer</li>
                    <li>Unity Certified Artist</li>
                </ul>
            </div>
            <div id="Certs">
                <div id="itf-Cert">

                </div>
                <div id="html-css-Cert">

                </div>
                <div id="Unity-prog-Cert">

                </div>
                <div id="Unity-vr-dev-Cert">

                </div>
                <div id="unity-Artist-Cert">

                </div>
            </div>
        </section>

        <div class="Line-Divider"></div>

        <section id="Contact" class="Main-Content">
            <h1>Contact Me!</h1>

            <form action="https://formsubmit.co/<EMAIL>" method="POST">
                <p>Name *</p>
                <input type="text" name="name" placeholder="John Smith" required>

                <p>Phone Number</p>
                <input type="number" name="PhoneNumber" placeholder="************">

                <p>Email *</p>
                <input type="email" name="email" placeholder="<EMAIL>" required>

                <p>Message *</p>
                <input type="text" name="Message" placeholder="Message" required>

                <br>

                <input type="submit">
                <p id="smallText">(* indicates required)</p>
            </form>
        </section>

        <div class="Line-Divider"></div>

        <!-- Testimonials Section -->
        <section id="Testimonials" class="Main-Content">
            <h1>What People Say</h1>
            <div class="testimonials-container">
                <div class="testimonial-card" data-aos="fade-up">
                    <div class="testimonial-content">
                        <p>"Ryan's technical expertise and problem-solving abilities are exceptional. He delivered our project
                        ahead of schedule with outstanding quality. His attention to detail and innovative approach made all
                        the difference."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="" alt="Client testimonial photo">
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <span>Project Manager, TechCorp</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="testimonial-content">
                        <p>"Working with Ryan was a fantastic experience. His knowledge of modern development practices and
                        ability to translate complex requirements into elegant solutions is truly impressive. Highly recommended!"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="" alt="Client testimonial photo">
                        <div class="author-info">
                            <h4>Michael Chen</h4>
                            <span>CTO, StartupXYZ</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="testimonial-content">
                        <p>"Ryan's VR development skills are top-notch. He created an immersive experience that exceeded our
                        expectations. His creativity and technical proficiency make him a valuable asset to any team."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="" alt="Client testimonial photo">
                        <div class="author-info">
                            <h4>Emily Rodriguez</h4>
                            <span>Creative Director, GameStudio</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>Ryan Haug</h3>
                <p>Software Engineer passionate about creating innovative solutions through code.</p>
                <div class="footer-social">
                    <a href="#" aria-label="LinkedIn">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                            <path fill="currentColor" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <a href="#" aria-label="GitHub">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                            <path fill="currentColor" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                    </a>
                    <a href="#" aria-label="Email">
                        <svg viewBox="0 0 24 24" width="20" height="20">
                            <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <div class="footer-section">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="#About">About</a></li>
                    <li><a href="#Projects">Projects</a></li>
                    <li><a href="#Experience">Experience</a></li>
                    <li><a href="#Certifications">Certifications</a></li>
                    <li><a href="#Contact">Contact</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h4>Contact Info</h4>
                <div class="contact-info">
                    <p>📧 <EMAIL></p>
                    <p>📍 Available for remote work</p>
                    <p>💼 Open to new opportunities</p>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <p>&copy; 2024 Ryan Haug. All rights reserved. Built with passion and modern web technologies.</p>
        </div>
    </footer>

    <script src="./Scripts/Main.js" defer></script>
</body>

</html>