// Portfolio JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Loading screen
    const loadingScreen = document.getElementById('loading-screen');

    // Hide loading screen after 2.5 seconds
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        // Initialize animations after loading
        initializeAnimations();
    }, 2500);

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme') || 'dark';
    body.setAttribute('data-theme', savedTheme);

    themeToggle.addEventListener('click', () => {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    });

    // Typing animation for hero text
    const typingText = document.getElementById('typing-text');
    const texts = [
        'Software Engineer',
        'Full-Stack Developer',
        'VR/AR Developer',
        'Game Developer',
        'Problem Solver'
    ];
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;

    function typeWriter() {
        const currentText = texts[textIndex];

        if (isDeleting) {
            typingText.textContent = currentText.substring(0, charIndex - 1);
            charIndex--;
        } else {
            typingText.textContent = currentText.substring(0, charIndex + 1);
            charIndex++;
        }

        let typeSpeed = isDeleting ? 50 : 100;

        if (!isDeleting && charIndex === currentText.length) {
            typeSpeed = 2000; // Pause at end
            isDeleting = true;
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            textIndex = (textIndex + 1) % texts.length;
            typeSpeed = 500; // Pause before next word
        }

        setTimeout(typeWriter, typeSpeed);
    }

    // Start typing animation after loading screen
    setTimeout(typeWriter, 3000);

    // Animated particles background
    createParticles();

    // Skill bars animation
    function animateSkillBars() {
        const skillBars = document.querySelectorAll('.skill-progress');
        skillBars.forEach(bar => {
            const width = bar.getAttribute('data-width');
            bar.style.setProperty('--target-width', width + '%');
            bar.classList.add('animate');
            bar.style.width = width + '%';
        });
    }

    // Initialize animations
    function initializeAnimations() {
        // Animate skill bars when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('skills-section')) {
                        setTimeout(animateSkillBars, 500);
                    }
                }
            });
        });

        const skillsSection = document.querySelector('.skills-section');
        if (skillsSection) {
            observer.observe(skillsSection);
        }

        // Add scroll-triggered animations for other elements
        const animatedElements = document.querySelectorAll('[data-aos]');
        animatedElements.forEach(el => {
            observer.observe(el);
        });
    }

    // Create animated particles
    function createParticles() {
        const particlesContainer = document.getElementById('particles-background');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 4 + 1}px;
                height: ${Math.random() * 4 + 1}px;
                background: var(--secondary-bg);
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${Math.random() * 10 + 10}s infinite linear;
                opacity: ${Math.random() * 0.5 + 0.1};
            `;
            particlesContainer.appendChild(particle);
        }

        // Add CSS animation for particles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0% { transform: translateY(100vh) rotate(0deg); }
                100% { transform: translateY(-100vh) rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    // Certification selector functionality
    const certificationItems = document.querySelectorAll('#Certification-Selector ul li');
    const certDisplayArea = document.querySelector('#Certs');

    // Certificate content data
    const certificateData = {
        'CompTIA ITF': {
            title: 'CompTIA IT Fundamentals',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
            date: 'Completed: 2023',
            skills: ['IT Fundamentals', 'Hardware', 'Software', 'Security Basics']
        },
        'Pearson HTML and CSS': {
            title: 'Pearson HTML and CSS Certification',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Mauris viverra veniam sit amet lacus.',
            date: 'Completed: 2023',
            skills: ['HTML5', 'CSS3', 'Responsive Design', 'Web Standards']
        },
        'Unity Certified Programmer': {
            title: 'Unity Certified Programmer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec auctor blandit quam, et molestie dolor. Praesent congue erat at massa. Sed cursus turpis a purus.',
            date: 'Completed: 2024',
            skills: ['C# Programming', 'Unity Engine', 'Game Development', 'Scripting']
        },
        'Unity Certified VR Developer': {
            title: 'Unity Certified VR Developer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam erat volutpat. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla.',
            date: 'Completed: 2024',
            skills: ['VR Development', 'Unity XR', 'Immersive Experiences', 'VR Optimization']
        },
        'Unity Certified Artist': {
            title: 'Unity Certified Artist',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum.',
            date: 'Completed: 2024',
            skills: ['3D Modeling', 'Texturing', 'Lighting', 'Animation']
        }
    };

    // Function to display certificate information
    function displayCertificate(certName) {
        const cert = certificateData[certName];
        if (cert) {
            certDisplayArea.innerHTML = `
                <div class="cert-display">
                    <h3>${cert.title}</h3>
                    <p class="cert-date">${cert.date}</p>
                    <p class="cert-description">${cert.description}</p>
                    <div class="cert-skills">
                        <h4>Key Skills:</h4>
                        <ul>
                            ${cert.skills.map(skill => `<li>${skill}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    // Add click event listeners to certification items
    certificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            certificationItems.forEach(i => i.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
            // Display the selected certificate
            displayCertificate(this.textContent);
        });
    });

    // Display first certificate by default
    if (certificationItems.length > 0) {
        certificationItems[0].classList.add('active');
        displayCertificate(certificationItems[0].textContent);
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('header nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Scroll indicator functionality
    const scrollIndicator = document.querySelector('#Home-Scroll');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const aboutSection = document.querySelector('#About');
            if (aboutSection) {
                aboutSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
});