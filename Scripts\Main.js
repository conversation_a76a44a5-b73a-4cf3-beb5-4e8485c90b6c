// Portfolio JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Loading screen
    const loadingScreen = document.getElementById('loading-screen');

    // Hide loading screen after 2.5 seconds
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        // Initialize animations after loading
        initializeAnimations();
    }, 2500);

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme') || 'dark';
    body.setAttribute('data-theme', savedTheme);

    themeToggle.addEventListener('click', () => {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    });

    // Typing animation for hero text
    const typingText = document.getElementById('typing-text');
    const texts = [
        'Software Engineer',
        'Full-Stack Developer',
        'VR/AR Developer',
        'Game Developer',
        'Problem Solver'
    ];
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;

    function typeWriter() {
        const currentText = texts[textIndex];

        if (isDeleting) {
            typingText.textContent = currentText.substring(0, charIndex - 1);
            charIndex--;
        } else {
            typingText.textContent = currentText.substring(0, charIndex + 1);
            charIndex++;
        }

        let typeSpeed = isDeleting ? 50 : 100;

        if (!isDeleting && charIndex === currentText.length) {
            typeSpeed = 2000; // Pause at end
            isDeleting = true;
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            textIndex = (textIndex + 1) % texts.length;
            typeSpeed = 500; // Pause before next word
        }

        setTimeout(typeWriter, typeSpeed);
    }

    // Start typing animation after loading screen
    setTimeout(typeWriter, 3000);

    // Animated particles background
    createParticles();

    // Skill bars animation
    function animateSkillBars() {
        const skillBars = document.querySelectorAll('.skill-progress');
        skillBars.forEach(bar => {
            const width = bar.getAttribute('data-width');
            bar.style.setProperty('--target-width', width + '%');
            bar.classList.add('animate');
            bar.style.width = width + '%';
        });
    }

    // Initialize animations
    function initializeAnimations() {
        // Animate skill bars when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('skills-section')) {
                        setTimeout(animateSkillBars, 500);
                    }
                }
            });
        });

        const skillsSection = document.querySelector('.skills-section');
        if (skillsSection) {
            observer.observe(skillsSection);
        }

        // Add scroll-triggered animations for other elements
        const animatedElements = document.querySelectorAll('[data-aos]');
        animatedElements.forEach(el => {
            observer.observe(el);
        });
    }

    // Create ethereal particles
    function createParticles() {
        const particlesContainer = document.getElementById('particles-background');
        const particleCount = 80;

        // Create different types of ethereal particles
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            const size = Math.random() * 6 + 2;
            const type = Math.random();

            let particleStyle = '';

            if (type < 0.3) {
                // Glowing orbs
                particleStyle = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    background: radial-gradient(circle, rgba(167, 85, 247, 0.8) 0%, rgba(167, 85, 247, 0.2) 50%, transparent 100%);
                    border-radius: 50%;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: etherealFloat ${Math.random() * 15 + 20}s infinite linear;
                    filter: blur(${Math.random() * 2}px);
                    box-shadow: 0 0 ${size * 2}px rgba(167, 85, 247, 0.5);
                `;
            } else if (type < 0.6) {
                // Mystical sparkles
                particleStyle = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    background: linear-gradient(45deg, rgba(59, 130, 246, 0.8), rgba(6, 182, 212, 0.8));
                    border-radius: 50%;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: etherealSparkle ${Math.random() * 12 + 15}s infinite linear;
                    filter: blur(${Math.random() * 1.5}px);
                    box-shadow: 0 0 ${size * 3}px rgba(59, 130, 246, 0.4);
                `;
            } else {
                // Dreamy wisps
                particleStyle = `
                    position: absolute;
                    width: ${size * 2}px;
                    height: ${size}px;
                    background: linear-gradient(90deg, transparent, rgba(236, 72, 153, 0.6), transparent);
                    border-radius: 50px;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: etherealWisp ${Math.random() * 18 + 25}s infinite linear;
                    filter: blur(${Math.random() * 3 + 1}px);
                    transform: rotate(${Math.random() * 360}deg);
                `;
            }

            particle.style.cssText = particleStyle;
            particlesContainer.appendChild(particle);
        }

        // Add ethereal CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes etherealFloat {
                0% {
                    transform: translateY(100vh) translateX(0) rotate(0deg) scale(0.5);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) translateX(${Math.random() * 200 - 100}px) rotate(360deg) scale(1.2);
                    opacity: 0;
                }
            }

            @keyframes etherealSparkle {
                0% {
                    transform: translateY(100vh) translateX(0) scale(0);
                    opacity: 0;
                }
                20% {
                    opacity: 1;
                    transform: translateY(80vh) translateX(${Math.random() * 100 - 50}px) scale(1);
                }
                80% {
                    opacity: 1;
                    transform: translateY(20vh) translateX(${Math.random() * 150 - 75}px) scale(1.5);
                }
                100% {
                    transform: translateY(-20vh) translateX(${Math.random() * 200 - 100}px) scale(0);
                    opacity: 0;
                }
            }

            @keyframes etherealWisp {
                0% {
                    transform: translateY(100vh) translateX(0) rotate(0deg) scaleX(0.5);
                    opacity: 0;
                }
                15% {
                    opacity: 0.8;
                }
                85% {
                    opacity: 0.8;
                }
                100% {
                    transform: translateY(-100vh) translateX(${Math.random() * 300 - 150}px) rotate(180deg) scaleX(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    // Certification selector functionality
    const certificationItems = document.querySelectorAll('#Certification-Selector ul li');
    const certDisplayArea = document.querySelector('#Certs');

    // Certificate content data
    const certificateData = {
        'CompTIA ITF': {
            title: 'CompTIA IT Fundamentals',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
            date: 'Completed: 2023',
            skills: ['IT Fundamentals', 'Hardware', 'Software', 'Security Basics']
        },
        'Pearson HTML and CSS': {
            title: 'Pearson HTML and CSS Certification',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Mauris viverra veniam sit amet lacus.',
            date: 'Completed: 2023',
            skills: ['HTML5', 'CSS3', 'Responsive Design', 'Web Standards']
        },
        'Unity Certified Programmer': {
            title: 'Unity Certified Programmer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec auctor blandit quam, et molestie dolor. Praesent congue erat at massa. Sed cursus turpis a purus.',
            date: 'Completed: 2024',
            skills: ['C# Programming', 'Unity Engine', 'Game Development', 'Scripting']
        },
        'Unity Certified VR Developer': {
            title: 'Unity Certified VR Developer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam erat volutpat. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla.',
            date: 'Completed: 2024',
            skills: ['VR Development', 'Unity XR', 'Immersive Experiences', 'VR Optimization']
        },
        'Unity Certified Artist': {
            title: 'Unity Certified Artist',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum.',
            date: 'Completed: 2024',
            skills: ['3D Modeling', 'Texturing', 'Lighting', 'Animation']
        }
    };

    // Function to display certificate information
    function displayCertificate(certName) {
        const cert = certificateData[certName];
        if (cert) {
            certDisplayArea.innerHTML = `
                <div class="cert-display">
                    <h3>${cert.title}</h3>
                    <p class="cert-date">${cert.date}</p>
                    <p class="cert-description">${cert.description}</p>
                    <div class="cert-skills">
                        <h4>Key Skills:</h4>
                        <ul>
                            ${cert.skills.map(skill => `<li>${skill}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    // Add click event listeners to certification items
    certificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            certificationItems.forEach(i => i.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
            // Display the selected certificate
            displayCertificate(this.textContent);
        });
    });

    // Display first certificate by default
    if (certificationItems.length > 0) {
        certificationItems[0].classList.add('active');
        displayCertificate(certificationItems[0].textContent);
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('header nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Scroll indicator functionality
    const scrollIndicator = document.querySelector('#Home-Scroll');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const aboutSection = document.querySelector('#About');
            if (aboutSection) {
                aboutSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
});