// Portfolio JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Certification selector functionality
    const certificationItems = document.querySelectorAll('#Certification-Selector ul li');
    const certDisplayArea = document.querySelector('#Certs');

    // Certificate content data
    const certificateData = {
        'CompTIA ITF': {
            title: 'CompTIA IT Fundamentals',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
            date: 'Completed: 2023',
            skills: ['IT Fundamentals', 'Hardware', 'Software', 'Security Basics']
        },
        'Pearson HTML and CSS': {
            title: 'Pearson HTML and CSS Certification',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Mauris viverra veniam sit amet lacus.',
            date: 'Completed: 2023',
            skills: ['HTML5', 'CSS3', 'Responsive Design', 'Web Standards']
        },
        'Unity Certified Programmer': {
            title: 'Unity Certified Programmer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec auctor blandit quam, et molestie dolor. Praesent congue erat at massa. Sed cursus turpis a purus.',
            date: 'Completed: 2024',
            skills: ['C# Programming', 'Unity Engine', 'Game Development', 'Scripting']
        },
        'Unity Certified VR Developer': {
            title: 'Unity Certified VR Developer',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam erat volutpat. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla.',
            date: 'Completed: 2024',
            skills: ['VR Development', 'Unity XR', 'Immersive Experiences', 'VR Optimization']
        },
        'Unity Certified Artist': {
            title: 'Unity Certified Artist',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum.',
            date: 'Completed: 2024',
            skills: ['3D Modeling', 'Texturing', 'Lighting', 'Animation']
        }
    };

    // Function to display certificate information
    function displayCertificate(certName) {
        const cert = certificateData[certName];
        if (cert) {
            certDisplayArea.innerHTML = `
                <div class="cert-display">
                    <h3>${cert.title}</h3>
                    <p class="cert-date">${cert.date}</p>
                    <p class="cert-description">${cert.description}</p>
                    <div class="cert-skills">
                        <h4>Key Skills:</h4>
                        <ul>
                            ${cert.skills.map(skill => `<li>${skill}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    // Add click event listeners to certification items
    certificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            certificationItems.forEach(i => i.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
            // Display the selected certificate
            displayCertificate(this.textContent);
        });
    });

    // Display first certificate by default
    if (certificationItems.length > 0) {
        certificationItems[0].classList.add('active');
        displayCertificate(certificationItems[0].textContent);
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('header nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Scroll indicator functionality
    const scrollIndicator = document.querySelector('#Home-Scroll');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const aboutSection = document.querySelector('#About');
            if (aboutSection) {
                aboutSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
});