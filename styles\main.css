/* Colors
bg -  #002b5d
accent 1 - aliceblue; (text, line div)
accent 2 - #348aa7; (anchor links)
*/

@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

:root {
    --primary-bg: radial-gradient(ellipse at center, #0a0a2e 0%, #16213e 35%, #0f3460 100%);
    --secondary-bg: #7c3aed;
    --accent-color: #e0e7ff;
    --text-color: #f8fafc;
    --ethereal-purple: #a855f7;
    --ethereal-blue: #3b82f6;
    --ethereal-cyan: #06b6d4;
    --ethereal-pink: #ec4899;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #a855f7 0%, #3b82f6 50%, #06b6d4 100%);
    --gradient-ethereal: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    --gradient-mystical: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    --shadow-ethereal: 0 8px 32px rgba(167, 85, 247, 0.3);
    --shadow-mystical: 0 15px 35px rgba(167, 85, 247, 0.4), 0 5px 15px rgba(59, 130, 246, 0.3);
    --glow-soft: 0 0 20px rgba(167, 85, 247, 0.5);
    --glow-intense: 0 0 40px rgba(167, 85, 247, 0.8), 0 0 60px rgba(59, 130, 246, 0.6);
    --transition-ethereal: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="light"] {
    --primary-bg: radial-gradient(ellipse at center, #fef7ff 0%, #f3e8ff 35%, #e9d5ff 100%);
    --secondary-bg: #7c3aed;
    --accent-color: #581c87;
    --text-color: #1e1b4b;
    --gradient-primary: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    --gradient-secondary: linear-gradient(135deg, #a855f7 0%, #3b82f6 50%, #06b6d4 100%);
}

body {
    font-family: "Nunito", sans-serif;
    background: var(--primary-bg);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    scroll-behavior: smooth;
    transition: var(--transition-ethereal);
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(167, 85, 247, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: etherealShift 20s ease-in-out infinite;
}

@keyframes etherealShift {
    0%, 100% {
        background:
            radial-gradient(circle at 20% 80%, rgba(167, 85, 247, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
    }
    33% {
        background:
            radial-gradient(circle at 70% 30%, rgba(167, 85, 247, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
    }
    66% {
        background:
            radial-gradient(circle at 50% 90%, rgba(167, 85, 247, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 90% 50%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 10% 10%, rgba(6, 182, 212, 0.15) 0%, transparent 50%);
    }
}

/* Ethereal Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-mystical);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 1s ease, visibility 1s ease;
    backdrop-filter: blur(10px);
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    text-align: center;
    position: relative;
}

.loader::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(167, 85, 247, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: etherealPulse 3s ease-in-out infinite;
}

.loader-text {
    font-size: 3.5em;
    font-weight: 300;
    margin-bottom: 2em;
    color: var(--accent-color);
    text-shadow: var(--glow-soft);
    letter-spacing: 0.1em;
}

.loader-text span {
    display: inline-block;
    animation: etherealFloat 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(167, 85, 247, 0.8));
}

.loader-text span:nth-child(1) { animation-delay: 0s; }
.loader-text span:nth-child(2) { animation-delay: 0.2s; }
.loader-text span:nth-child(3) { animation-delay: 0.4s; }
.loader-text span:nth-child(4) { animation-delay: 0.6s; }
.loader-text span:nth-child(6) { animation-delay: 1s; }
.loader-text span:nth-child(7) { animation-delay: 1.2s; }
.loader-text span:nth-child(8) { animation-delay: 1.4s; }
.loader-text span:nth-child(9) { animation-delay: 1.6s; }

.loader-bar {
    width: 400px;
    height: 6px;
    background: rgba(167, 85, 247, 0.2);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--glow-soft);
    backdrop-filter: blur(5px);
}

.loader-progress {
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 10px;
    animation: etherealProgress 3s ease-in-out;
    box-shadow: var(--glow-intense);
}

@keyframes etherealFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-15px) scale(1.1);
        opacity: 0.8;
    }
}

@keyframes etherealPulse {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.6;
    }
}

@keyframes etherealProgress {
    0% {
        width: 0%;
        box-shadow: var(--glow-soft);
    }
    50% {
        box-shadow: var(--glow-intense);
    }
    100% {
        width: 100%;
        box-shadow: var(--glow-soft);
    }
}

/* Animated Background Particles */
#particles-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

/* Ethereal Theme Toggle */
#theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    background: var(--gradient-secondary);
    color: var(--accent-color);
    cursor: pointer;
    z-index: 1000;
    transition: var(--transition-ethereal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4em;
    box-shadow: var(--shadow-ethereal);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(167, 85, 247, 0.3);
}

#theme-toggle:hover {
    transform: scale(1.15) rotate(180deg);
    box-shadow: var(--shadow-mystical);
    background: var(--gradient-mystical);
}

#theme-toggle::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: var(--transition-ethereal);
}

#theme-toggle:hover::before {
    opacity: 0.3;
    animation: etherealRipple 1s ease-out;
}

@keyframes etherealRipple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

[data-theme="light"] #theme-toggle .sun-icon {
    display: none;
}

[data-theme="dark"] #theme-toggle .moon-icon,
#theme-toggle .moon-icon {
    display: none;
}

[data-theme="light"] #theme-toggle .moon-icon {
    display: inline;
}

/* Ethereal Line Divider */
.Line-Divider {
    background: var(--gradient-secondary);
    display: block;
    height: 3px;
    margin-bottom: 0;
    border-radius: 2px;
    box-shadow: var(--glow-soft);
    position: relative;
    overflow: hidden;
}

.Line-Divider::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: etherealShimmer 3s ease-in-out infinite;
}

@keyframes etherealShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.Main-Content {
    font-size: 1.6em;
    margin: 8em 0;
}

h1 {
    margin-bottom: 1em;
    font-weight: 300;
    color: var(--accent-color);
    text-shadow: var(--glow-soft);
    letter-spacing: 0.02em;
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: 2px;
    box-shadow: var(--glow-soft);
}

h2 {
    font-size: 2.8em;
    margin-bottom: 0.5em;
    font-weight: 200;
    color: var(--accent-color);
    text-shadow: var(--glow-soft);
    letter-spacing: 0.05em;
    background: var(--gradient-secondary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: etherealGlow 4s ease-in-out infinite;
}

h3 {
    font-size: 1.8em;
    margin: 2em 0 1em 0;
    text-align: center;
    color: var(--ethereal-purple);
    font-weight: 400;
    text-shadow: var(--glow-soft);
    letter-spacing: 0.03em;
}

@keyframes etherealGlow {
    0%, 100% {
        filter: brightness(1) saturate(1);
        text-shadow: 0 0 20px rgba(167, 85, 247, 0.5);
    }
    50% {
        filter: brightness(1.2) saturate(1.3);
        text-shadow: 0 0 30px rgba(167, 85, 247, 0.8), 0 0 40px rgba(59, 130, 246, 0.6);
    }
}

/* Image placeholders */
img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

img[src=""], img:not([src]) {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-weight: 600;
    text-align: center;
    min-height: 200px;
    position: relative;
}

img[src=""]:after, img:not([src]):after {
    content: attr(alt);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Ethereal Header */
header {
    display: grid;
    grid-template-columns: 1fr .4fr 1fr;
    grid-template-rows: 1fr .2fr;
    justify-items: center;
    align-items: center;
    position: fixed;
    width: 100%;
    background: rgba(10, 10, 46, 0.3);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(167, 85, 247, 0.2);
    z-index: 100;
    transition: var(--transition-ethereal);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-secondary);
    opacity: 0.1;
    z-index: -1;
}

header>h1 {
    margin: 0.4em 1em;
    color: var(--accent-color);
    text-shadow: var(--glow-soft);
    font-weight: 300;
    letter-spacing: 0.05em;
}

header>nav:first-of-type {
    justify-self: end;
}

header>nav:last-of-type {
    justify-self: start;
}

header>nav>a {
    margin: 0 1em;
    color: var(--ethereal-purple);
    text-decoration: none;
    font-weight: 400;
    transition: var(--transition-ethereal);
    padding: 0.8em 1.5em;
    border-radius: 25px;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(167, 85, 247, 0.2);
    background: rgba(167, 85, 247, 0.1);
}

header>nav>a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-secondary);
    border-radius: 25px;
    opacity: 0;
    transition: var(--transition-ethereal);
    z-index: -1;
}

header>nav>a:hover {
    color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-ethereal);
    text-shadow: var(--glow-soft);
}

header>nav>a:hover::before {
    opacity: 1;
}

header>div {
    align-self: normal;
    grid-column: span 3;

    width: 85%;
    transform: translate(0, 10px);
}

/* Home Content */

#Home {
    display: grid;
    grid-template-columns: 2.5fr 2fr;
    grid-template-rows: 1fr .1fr;

    height: 100vh;
}

#Home-Image {
    margin: 0 5em;

    justify-self: center;
    align-self: center;
}

#Home-Content {
    display: flex;
    flex-direction: column;

    align-self: center;

    width: 80%;
}

#Home-Content>.Line-Divider {
    background: linear-gradient(to right, var(--accent-color) 70%, transparent);
    width: 40%;
}

/* Ethereal Social Links */
.social-links {
    display: flex;
    gap: 2em;
    margin-top: 3em;
    justify-content: flex-start;
}

.social-link {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-ethereal);
    box-shadow: var(--shadow-ethereal);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(167, 85, 247, 0.3);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(167, 85, 247, 0.3), transparent);
    animation: etherealRotate 3s linear infinite;
    opacity: 0;
    transition: var(--transition-ethereal);
}

.social-link:hover::before {
    opacity: 1;
}

.social-link:hover {
    transform: translateY(-8px) scale(1.15);
    box-shadow: var(--shadow-mystical);
    background: var(--gradient-mystical);
}

.social-link svg {
    width: 28px;
    height: 28px;
    z-index: 1;
    filter: drop-shadow(0 0 5px rgba(167, 85, 247, 0.5));
}

@keyframes etherealRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#Home-Scroll {
    grid-column: span 2;
    justify-self: center;
    animation: bounce 2s infinite;
    cursor: pointer;
}

#Home-Scroll p {
    background: linear-gradient(45deg, #348aa7, aliceblue);
    padding: 1em 2em;
    border-radius: 25px;
    margin: 0;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
}

#Home-Scroll:hover p {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.5);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* About Content */

#About {
    display: flex;
    flex-wrap: wrap;

    justify-content: center;
    align-items: center;

    padding: 0 6em;

    height: 70vh;
}

#About>h1 {
    width: 100%;
    text-align: center;
}

#About>div {
    width: 40%;
}

.About-Photo {
    justify-self: center;
    margin: 1em;
}

.About-Photo img {
    max-width: 300px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.About-Photo img:hover {
    transform: scale(1.05);
}

#About>.About-Text {
    width: 60%;
    margin: 1em;
}

#About>.About-Text p {
    font-size: 1.1em;
    text-align: justify;
    margin-bottom: 1.5em;
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2em;
    margin-top: 3em;
}

.project-card {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(52, 138, 167, 0.2);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 43, 93, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 1em;
}

.project-link {
    padding: 0.8em 1.5em;
    background: var(--gradient-secondary);
    color: var(--accent-color);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
}

.project-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.project-content {
    padding: 2em;
}

.project-content h3 {
    color: var(--secondary-bg);
    margin-bottom: 1em;
    font-size: 1.4em;
}

.project-content p {
    margin-bottom: 1.5em;
    line-height: 1.6;
    color: var(--text-color);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.tech-tag {
    background: var(--gradient-secondary);
    color: var(--accent-color);
    padding: 0.3em 0.8em;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 500;
    transition: var(--transition-smooth);
}

.tech-tag:hover {
    transform: scale(1.05);
}

/* Experience content */

#Experience {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    padding: 4em 2em;
}

#Experience > h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 2em;
}

#Language-Photos {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
    margin-bottom: 3em;
}

#Experience-Tools {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
}

#Language-Photos > div,
#Experience-Tools > div {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

#Language-Photos > div:hover,
#Experience-Tools > div:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 12px 30px rgba(52, 138, 167, 0.5);
}

/* Enhanced Experience Section */
.experience-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4em;
    margin-top: 3em;
}

.skills-section, .tools-section {
    background: rgba(52, 138, 167, 0.1);
    padding: 2em;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.skills-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
    margin-top: 2em;
}

.skill-item {
    background: rgba(240, 248, 255, 0.05);
    padding: 1.5em;
    border-radius: 10px;
    transition: var(--transition-smooth);
}

.skill-item:hover {
    transform: translateX(10px);
    background: rgba(240, 248, 255, 0.1);
}

.skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8em;
    font-weight: 600;
}

.skill-name {
    color: var(--text-color);
}

.skill-percentage {
    color: var(--secondary-bg);
}

.skill-bar {
    height: 8px;
    background: rgba(52, 138, 167, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 4px;
    width: 0%;
    transition: width 2s ease-in-out;
}

.skill-progress.animate {
    width: var(--target-width);
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1em;
}

.tool-item img {
    max-width: 80px;
    max-height: 80px;
    filter: brightness(1.1);
}

.tool-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9em;
}

/* Certifications */
#Certifications {
    min-height: 60vh;
    width: 90%;
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: auto 1fr;
    gap: 2em;
    justify-self: center;
    margin: 4em auto;
    padding: 2em;
}

#Certifications > h1 {
    grid-column: span 2;
    width: 100%;
    text-align: center;
    margin-bottom: 1em;
}

#Certification-Selector {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 1.5em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#Certification-Selector > ul {
    list-style: none;
    padding: 0;
    margin: 0;
    height: 100%;
}

#Certification-Selector > ul > li {
    padding: 1em 1.5em;
    margin-bottom: 0.5em;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 138, 167, 0.3);
}

#Certification-Selector > ul > li:hover {
    transform: translateX(10px);
    background: linear-gradient(135deg, #5ba3c7, #348aa7);
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.5);
}

#Certification-Selector > ul > li.active {
    background: linear-gradient(135deg, aliceblue, #e6f3ff);
    color: #002b5d;
    transform: translateX(15px);
    box-shadow: 0 6px 20px rgba(240, 248, 255, 0.4);
}

#Certs {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 2em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

#Certs > div {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-size: 1.2em;
    font-weight: 600;
    text-align: center;
}

#Certs > div:after {
    content: "Certificate Display Area\A(Click certification to view)";
    white-space: pre-line;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Certificate display styles */
.cert-display {
    padding: 2em;
    background: rgba(240, 248, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(52, 138, 167, 0.3);
    animation: fadeIn 0.5s ease-in;
}

.cert-display h3 {
    color: #348aa7;
    margin-bottom: 0.5em;
    font-size: 1.5em;
}

.cert-date {
    color: rgba(240, 248, 255, 0.8);
    font-style: italic;
    margin-bottom: 1em;
    font-size: 0.9em;
}

.cert-description {
    line-height: 1.6;
    margin-bottom: 1.5em;
    text-align: justify;
}

.cert-skills h4 {
    color: #348aa7;
    margin-bottom: 0.5em;
    font-size: 1.1em;
}

.cert-skills ul {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.cert-skills li {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    color: aliceblue;
    padding: 0.3em 0.8em;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 500;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Contact */
#Contact {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 4em auto;
    padding: 2em;
    max-width: 800px;
}

#Contact > h1 {
    margin-bottom: 2em;
}

#Contact > form {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    padding: 2em;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(52, 138, 167, 0.4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1em;
    width: 100%;
    max-width: 600px;
    transition: transform 0.3s ease;
}

#Contact > form:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(52, 138, 167, 0.6);
}

#Contact > form > p {
    margin: 0;
    font-weight: 600;
    color: aliceblue;
    text-align: left;
}

#Contact > form > input {
    padding: 0.8em;
    border: none;
    border-radius: 8px;
    font-family: "Nunito", sans-serif;
    font-size: 1em;
    background: rgba(240, 248, 255, 0.9);
    transition: all 0.3s ease;
    margin: 0;
}

#Contact > form > input:focus {
    outline: none;
    background: aliceblue;
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(240, 248, 255, 0.5);
}

#Contact > form > input[type="submit"] {
    grid-column: span 2;
    background: linear-gradient(45deg, #002b5d, #1a4a7a);
    color: aliceblue;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1em;
    transition: all 0.3s ease;
}

#Contact > form > input[type="submit"]:hover {
    background: linear-gradient(45deg, #1a4a7a, #002b5d);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 43, 93, 0.4);
}

#Contact > form > input[name="Message"] {
    grid-column: span 2;
    min-height: 100px;
    resize: vertical;
}

#Contact > form > p[for="Message"] {
    grid-column: span 2;
}

#smallText {
    font-size: 0.8em;
    grid-column: span 2;
    margin-top: 0.5em;
    color: rgba(240, 248, 255, 0.8);
}

/* Testimonials Section */
.testimonials-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
    margin-top: 3em;
}

.testimonial-card {
    background: rgba(52, 138, 167, 0.1);
    padding: 2em;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    transition: var(--transition-smooth);
    border: 1px solid rgba(52, 138, 167, 0.2);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.testimonial-content {
    margin-bottom: 2em;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1em;
    line-height: 1.6;
    color: var(--text-color);
    position: relative;
}

.testimonial-content p:before {
    content: '"';
    font-size: 3em;
    color: var(--secondary-bg);
    position: absolute;
    top: -10px;
    left: -20px;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1em;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--secondary-bg);
}

.author-info h4 {
    margin: 0;
    color: var(--secondary-bg);
    font-weight: 600;
}

.author-info span {
    color: var(--text-color);
    opacity: 0.8;
    font-size: 0.9em;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    margin-top: 4em;
    padding: 3em 0 1em 0;
    border-top: 1px solid rgba(52, 138, 167, 0.3);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2em;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2em;
}

.footer-section h3 {
    color: var(--accent-color);
    margin-bottom: 1em;
    font-size: 1.5em;
}

.footer-section h4 {
    color: var(--secondary-bg);
    margin-bottom: 1em;
    font-size: 1.2em;
}

.footer-section p {
    color: var(--text-color);
    opacity: 0.9;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1em;
    margin-top: 1em;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-smooth);
}

.footer-social a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-light);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5em;
}

.footer-section ul li a {
    color: var(--text-color);
    text-decoration: none;
    opacity: 0.9;
    transition: var(--transition-smooth);
}

.footer-section ul li a:hover {
    color: var(--secondary-bg);
    opacity: 1;
}

.contact-info p {
    margin: 0.5em 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
}

.footer-bottom {
    text-align: center;
    margin-top: 2em;
    padding-top: 2em;
    border-top: 1px solid rgba(52, 138, 167, 0.3);
    color: var(--text-color);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        padding: 1em;
    }

    header > h1 {
        order: 1;
        margin: 0.5em 0;
    }

    header > nav:first-of-type {
        order: 2;
        justify-self: center;
    }

    header > nav:last-of-type {
        order: 3;
        justify-self: center;
    }

    header > div {
        order: 4;
        width: 90%;
    }

    #Home {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
        padding: 2em 1em;
    }

    #Home-Image {
        order: 1;
        margin: 2em 1em;
    }

    #Home-Content {
        order: 2;
        width: 100%;
        text-align: center;
    }

    #Home-Scroll {
        order: 3;
        grid-column: 1;
    }

    #About {
        flex-direction: column;
        height: auto;
        padding: 2em 1em;
    }

    #About > div {
        width: 100%;
        margin: 1em 0;
    }

    #About > .About-Text {
        width: 100%;
    }

    #Experience {
        padding: 2em 1em;
    }

    #Language-Photos,
    #Experience-Tools {
        width: 100%;
        justify-content: center;
    }

    #Language-Photos > div,
    #Experience-Tools > div {
        width: 100px;
        height: 100px;
    }

    #Certifications {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        width: 95%;
        padding: 1em;
    }

    #Contact > form {
        grid-template-columns: 1fr;
        padding: 1.5em;
    }

    #Contact > form > input[name="Message"] {
        grid-column: 1;
    }

    #Contact > form > input[type="submit"] {
        grid-column: 1;
    }

    #smallText {
        grid-column: 1;
    }
}

@media (max-width: 480px) {
    .Main-Content {
        font-size: 1.2em;
        margin: 4em 0;
    }

    h2 {
        font-size: 2em;
    }

    h3 {
        font-size: 1.5em;
    }

    #Language-Photos > div,
    #Experience-Tools > div {
        width: 80px;
        height: 80px;
    }

    #Language-Photos img,
    #Experience-Tools img {
        max-width: 60px;
        max-height: 60px;
    }
}