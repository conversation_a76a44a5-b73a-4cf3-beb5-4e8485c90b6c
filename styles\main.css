/* Colors
bg -  #002b5d
accent 1 - aliceblue; (text, line div)
accent 2 - #348aa7; (anchor links)
*/

@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

:root {
    --primary-bg: #002b5d;
    --secondary-bg: #348aa7;
    --accent-color: aliceblue;
    --text-color: aliceblue;
    --gradient-primary: linear-gradient(135deg, #002b5d, #348aa7);
    --gradient-secondary: linear-gradient(135deg, #348aa7, #5ba3c7);
    --shadow-light: 0 4px 15px rgba(52, 138, 167, 0.3);
    --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.3);
    --transition-smooth: all 0.3s ease;
}

[data-theme="light"] {
    --primary-bg: #f0f8ff;
    --secondary-bg: #348aa7;
    --accent-color: #002b5d;
    --text-color: #002b5d;
    --gradient-primary: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    --gradient-secondary: linear-gradient(135deg, #348aa7, #5ba3c7);
}

body {
    font-family: "Nunito", sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    scroll-behavior: smooth;
    transition: var(--transition-smooth);
    overflow-x: hidden;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    text-align: center;
}

.loader-text {
    font-size: 3em;
    font-weight: 700;
    margin-bottom: 2em;
    color: var(--accent-color);
}

.loader-text span {
    display: inline-block;
    animation: letterBounce 1.5s infinite;
}

.loader-text span:nth-child(1) { animation-delay: 0s; }
.loader-text span:nth-child(2) { animation-delay: 0.1s; }
.loader-text span:nth-child(3) { animation-delay: 0.2s; }
.loader-text span:nth-child(4) { animation-delay: 0.3s; }
.loader-text span:nth-child(6) { animation-delay: 0.5s; }
.loader-text span:nth-child(7) { animation-delay: 0.6s; }
.loader-text span:nth-child(8) { animation-delay: 0.7s; }
.loader-text span:nth-child(9) { animation-delay: 0.8s; }

.loader-bar {
    width: 300px;
    height: 4px;
    background: rgba(240, 248, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.loader-progress {
    height: 100%;
    background: var(--accent-color);
    border-radius: 2px;
    animation: loadingProgress 2s ease-in-out;
}

@keyframes letterBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-20px); }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* Animated Background Particles */
#particles-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

/* Theme Toggle */
#theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--gradient-secondary);
    color: var(--accent-color);
    cursor: pointer;
    z-index: 1000;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    box-shadow: var(--shadow-light);
}

#theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

[data-theme="light"] #theme-toggle .sun-icon {
    display: none;
}

[data-theme="dark"] #theme-toggle .moon-icon,
#theme-toggle .moon-icon {
    display: none;
}

[data-theme="light"] #theme-toggle .moon-icon {
    display: inline;
}

.Line-Divider {
    background: linear-gradient(to right, transparent, aliceblue, aliceblue, transparent);
    display: block;

    height: 2px;
    margin-bottom: 0;
}

.Main-Content {
    font-size: 1.6em;
    margin: 8em 0;
}

h1 {
    margin-bottom: 1em;
    font-weight: 700;
}

h2 {
    font-size: 2.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

h3 {
    font-size: 1.8em;
    margin: 2em 0 1em 0;
    text-align: center;
    color: #348aa7;
    font-weight: 600;
}

/* Image placeholders */
img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

img[src=""], img:not([src]) {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-weight: 600;
    text-align: center;
    min-height: 200px;
    position: relative;
}

img[src=""]:after, img:not([src]):after {
    content: attr(alt);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* header */

header {
    display: grid;
    grid-template-columns: 1fr .4fr 1fr;
    grid-template-rows: 1fr .2fr;

    justify-items: center;
    align-items: center;

    position: fixed;
    width: 100%;

    background-color: #002b5d54;
    backdrop-filter: blur(10px);
}

header>h1 {
    margin: 0.4em 1em;
}

header>nav:first-of-type {
    justify-self: end;
}

header>nav:last-of-type {
    justify-self: start;
}

header>nav>a {
    margin: 0 1em;
    color: #348aa7;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5em 1em;
    border-radius: 4px;
}

header>nav>a:hover {
    color: aliceblue;
    background-color: #348aa7;
    transform: translateY(-2px);
}

header>div {
    align-self: normal;
    grid-column: span 3;

    width: 85%;
    transform: translate(0, 10px);
}

/* Home Content */

#Home {
    display: grid;
    grid-template-columns: 2.5fr 2fr;
    grid-template-rows: 1fr .1fr;

    height: 100vh;
}

#Home-Image {
    margin: 0 5em;

    justify-self: center;
    align-self: center;
}

#Home-Content {
    display: flex;
    flex-direction: column;

    align-self: center;

    width: 80%;
}

#Home-Content>.Line-Divider {
    background: linear-gradient(to right, var(--accent-color) 70%, transparent);
    width: 40%;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1.5em;
    margin-top: 2em;
    justify-content: flex-start;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
}

.social-link:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.social-link svg {
    width: 24px;
    height: 24px;
}

#Home-Scroll {
    grid-column: span 2;
    justify-self: center;
    animation: bounce 2s infinite;
    cursor: pointer;
}

#Home-Scroll p {
    background: linear-gradient(45deg, #348aa7, aliceblue);
    padding: 1em 2em;
    border-radius: 25px;
    margin: 0;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
}

#Home-Scroll:hover p {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.5);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* About Content */

#About {
    display: flex;
    flex-wrap: wrap;

    justify-content: center;
    align-items: center;

    padding: 0 6em;

    height: 70vh;
}

#About>h1 {
    width: 100%;
    text-align: center;
}

#About>div {
    width: 40%;
}

.About-Photo {
    justify-self: center;
    margin: 1em;
}

.About-Photo img {
    max-width: 300px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.About-Photo img:hover {
    transform: scale(1.05);
}

#About>.About-Text {
    width: 60%;
    margin: 1em;
}

#About>.About-Text p {
    font-size: 1.1em;
    text-align: justify;
    margin-bottom: 1.5em;
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2em;
    margin-top: 3em;
}

.project-card {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(52, 138, 167, 0.2);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 43, 93, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 1em;
}

.project-link {
    padding: 0.8em 1.5em;
    background: var(--gradient-secondary);
    color: var(--accent-color);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
}

.project-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.project-content {
    padding: 2em;
}

.project-content h3 {
    color: var(--secondary-bg);
    margin-bottom: 1em;
    font-size: 1.4em;
}

.project-content p {
    margin-bottom: 1.5em;
    line-height: 1.6;
    color: var(--text-color);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.tech-tag {
    background: var(--gradient-secondary);
    color: var(--accent-color);
    padding: 0.3em 0.8em;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 500;
    transition: var(--transition-smooth);
}

.tech-tag:hover {
    transform: scale(1.05);
}

/* Experience content */

#Experience {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    padding: 4em 2em;
}

#Experience > h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 2em;
}

#Language-Photos {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
    margin-bottom: 3em;
}

#Experience-Tools {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
}

#Language-Photos > div,
#Experience-Tools > div {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

#Language-Photos > div:hover,
#Experience-Tools > div:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 12px 30px rgba(52, 138, 167, 0.5);
}

/* Enhanced Experience Section */
.experience-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4em;
    margin-top: 3em;
}

.skills-section, .tools-section {
    background: rgba(52, 138, 167, 0.1);
    padding: 2em;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.skills-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
    margin-top: 2em;
}

.skill-item {
    background: rgba(240, 248, 255, 0.05);
    padding: 1.5em;
    border-radius: 10px;
    transition: var(--transition-smooth);
}

.skill-item:hover {
    transform: translateX(10px);
    background: rgba(240, 248, 255, 0.1);
}

.skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8em;
    font-weight: 600;
}

.skill-name {
    color: var(--text-color);
}

.skill-percentage {
    color: var(--secondary-bg);
}

.skill-bar {
    height: 8px;
    background: rgba(52, 138, 167, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 4px;
    width: 0%;
    transition: width 2s ease-in-out;
}

.skill-progress.animate {
    width: var(--target-width);
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1em;
}

.tool-item img {
    max-width: 80px;
    max-height: 80px;
    filter: brightness(1.1);
}

.tool-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9em;
}

/* Certifications */
#Certifications {
    min-height: 60vh;
    width: 90%;
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: auto 1fr;
    gap: 2em;
    justify-self: center;
    margin: 4em auto;
    padding: 2em;
}

#Certifications > h1 {
    grid-column: span 2;
    width: 100%;
    text-align: center;
    margin-bottom: 1em;
}

#Certification-Selector {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 1.5em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#Certification-Selector > ul {
    list-style: none;
    padding: 0;
    margin: 0;
    height: 100%;
}

#Certification-Selector > ul > li {
    padding: 1em 1.5em;
    margin-bottom: 0.5em;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 138, 167, 0.3);
}

#Certification-Selector > ul > li:hover {
    transform: translateX(10px);
    background: linear-gradient(135deg, #5ba3c7, #348aa7);
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.5);
}

#Certification-Selector > ul > li.active {
    background: linear-gradient(135deg, aliceblue, #e6f3ff);
    color: #002b5d;
    transform: translateX(15px);
    box-shadow: 0 6px 20px rgba(240, 248, 255, 0.4);
}

#Certs {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 2em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

#Certs > div {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-size: 1.2em;
    font-weight: 600;
    text-align: center;
}

#Certs > div:after {
    content: "Certificate Display Area\A(Click certification to view)";
    white-space: pre-line;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Certificate display styles */
.cert-display {
    padding: 2em;
    background: rgba(240, 248, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(52, 138, 167, 0.3);
    animation: fadeIn 0.5s ease-in;
}

.cert-display h3 {
    color: #348aa7;
    margin-bottom: 0.5em;
    font-size: 1.5em;
}

.cert-date {
    color: rgba(240, 248, 255, 0.8);
    font-style: italic;
    margin-bottom: 1em;
    font-size: 0.9em;
}

.cert-description {
    line-height: 1.6;
    margin-bottom: 1.5em;
    text-align: justify;
}

.cert-skills h4 {
    color: #348aa7;
    margin-bottom: 0.5em;
    font-size: 1.1em;
}

.cert-skills ul {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.cert-skills li {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    color: aliceblue;
    padding: 0.3em 0.8em;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 500;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Contact */
#Contact {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 4em auto;
    padding: 2em;
    max-width: 800px;
}

#Contact > h1 {
    margin-bottom: 2em;
}

#Contact > form {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    padding: 2em;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(52, 138, 167, 0.4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1em;
    width: 100%;
    max-width: 600px;
    transition: transform 0.3s ease;
}

#Contact > form:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(52, 138, 167, 0.6);
}

#Contact > form > p {
    margin: 0;
    font-weight: 600;
    color: aliceblue;
    text-align: left;
}

#Contact > form > input {
    padding: 0.8em;
    border: none;
    border-radius: 8px;
    font-family: "Nunito", sans-serif;
    font-size: 1em;
    background: rgba(240, 248, 255, 0.9);
    transition: all 0.3s ease;
    margin: 0;
}

#Contact > form > input:focus {
    outline: none;
    background: aliceblue;
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(240, 248, 255, 0.5);
}

#Contact > form > input[type="submit"] {
    grid-column: span 2;
    background: linear-gradient(45deg, #002b5d, #1a4a7a);
    color: aliceblue;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1em;
    transition: all 0.3s ease;
}

#Contact > form > input[type="submit"]:hover {
    background: linear-gradient(45deg, #1a4a7a, #002b5d);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 43, 93, 0.4);
}

#Contact > form > input[name="Message"] {
    grid-column: span 2;
    min-height: 100px;
    resize: vertical;
}

#Contact > form > p[for="Message"] {
    grid-column: span 2;
}

#smallText {
    font-size: 0.8em;
    grid-column: span 2;
    margin-top: 0.5em;
    color: rgba(240, 248, 255, 0.8);
}

/* Testimonials Section */
.testimonials-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
    margin-top: 3em;
}

.testimonial-card {
    background: rgba(52, 138, 167, 0.1);
    padding: 2em;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    transition: var(--transition-smooth);
    border: 1px solid rgba(52, 138, 167, 0.2);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.testimonial-content {
    margin-bottom: 2em;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1em;
    line-height: 1.6;
    color: var(--text-color);
    position: relative;
}

.testimonial-content p:before {
    content: '"';
    font-size: 3em;
    color: var(--secondary-bg);
    position: absolute;
    top: -10px;
    left: -20px;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1em;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--secondary-bg);
}

.author-info h4 {
    margin: 0;
    color: var(--secondary-bg);
    font-weight: 600;
}

.author-info span {
    color: var(--text-color);
    opacity: 0.8;
    font-size: 0.9em;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    margin-top: 4em;
    padding: 3em 0 1em 0;
    border-top: 1px solid rgba(52, 138, 167, 0.3);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2em;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2em;
}

.footer-section h3 {
    color: var(--accent-color);
    margin-bottom: 1em;
    font-size: 1.5em;
}

.footer-section h4 {
    color: var(--secondary-bg);
    margin-bottom: 1em;
    font-size: 1.2em;
}

.footer-section p {
    color: var(--text-color);
    opacity: 0.9;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1em;
    margin-top: 1em;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-smooth);
}

.footer-social a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-light);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5em;
}

.footer-section ul li a {
    color: var(--text-color);
    text-decoration: none;
    opacity: 0.9;
    transition: var(--transition-smooth);
}

.footer-section ul li a:hover {
    color: var(--secondary-bg);
    opacity: 1;
}

.contact-info p {
    margin: 0.5em 0;
    display: flex;
    align-items: center;
    gap: 0.5em;
}

.footer-bottom {
    text-align: center;
    margin-top: 2em;
    padding-top: 2em;
    border-top: 1px solid rgba(52, 138, 167, 0.3);
    color: var(--text-color);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        padding: 1em;
    }

    header > h1 {
        order: 1;
        margin: 0.5em 0;
    }

    header > nav:first-of-type {
        order: 2;
        justify-self: center;
    }

    header > nav:last-of-type {
        order: 3;
        justify-self: center;
    }

    header > div {
        order: 4;
        width: 90%;
    }

    #Home {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
        padding: 2em 1em;
    }

    #Home-Image {
        order: 1;
        margin: 2em 1em;
    }

    #Home-Content {
        order: 2;
        width: 100%;
        text-align: center;
    }

    #Home-Scroll {
        order: 3;
        grid-column: 1;
    }

    #About {
        flex-direction: column;
        height: auto;
        padding: 2em 1em;
    }

    #About > div {
        width: 100%;
        margin: 1em 0;
    }

    #About > .About-Text {
        width: 100%;
    }

    #Experience {
        padding: 2em 1em;
    }

    #Language-Photos,
    #Experience-Tools {
        width: 100%;
        justify-content: center;
    }

    #Language-Photos > div,
    #Experience-Tools > div {
        width: 100px;
        height: 100px;
    }

    #Certifications {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        width: 95%;
        padding: 1em;
    }

    #Contact > form {
        grid-template-columns: 1fr;
        padding: 1.5em;
    }

    #Contact > form > input[name="Message"] {
        grid-column: 1;
    }

    #Contact > form > input[type="submit"] {
        grid-column: 1;
    }

    #smallText {
        grid-column: 1;
    }
}

@media (max-width: 480px) {
    .Main-Content {
        font-size: 1.2em;
        margin: 4em 0;
    }

    h2 {
        font-size: 2em;
    }

    h3 {
        font-size: 1.5em;
    }

    #Language-Photos > div,
    #Experience-Tools > div {
        width: 80px;
        height: 80px;
    }

    #Language-Photos img,
    #Experience-Tools img {
        max-width: 60px;
        max-height: 60px;
    }
}