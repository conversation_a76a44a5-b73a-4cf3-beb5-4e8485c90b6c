/* Colors
bg -  #002b5d
accent 1 - aliceblue; (text, line div)
accent 2 - #348aa7; (anchor links)
*/

@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

body {
    font-family: "Nunito" sans-serif;

    background-color: #002b5d;
    color: aliceblue;

    margin-top: 0;
}

.Line-Divider {
    background: linear-gradient(to right, transparent, aliceblue, aliceblue, transparent);
    display: block;

    height: 2px;
    margin-bottom: 0;
}

.Main-Content {
    font-size: 1.6em;
    margin: 8em 0;
}

h1 {
    margin-bottom: 1em;
}

/* header */

header {
    display: grid;
    grid-template-columns: 1fr .4fr 1fr;
    grid-template-rows: 1fr .2fr;

    justify-items: center;
    align-items: center;

    position: fixed;
    width: 100%;
    
    background-color: #002b5d54;
    backdrop-filter: blur(10px);
}

header>h1 {
    margin: 0.4em 1em;
}

header>nav:first-of-type {
    justify-self: end;
}

header>nav:last-of-type {
    justify-self: start;
}

header>nav>a {
    margin: 0 1em;
    color: rgb(146, 166, 255);
}

header>div {
    align-self: normal;
    grid-column: span 3;

    width: 85%;
    transform: translate(0, 10px);
}

/* Home Content */

#Home {
    display: grid;
    grid-template-columns: 2.5fr 2fr;
    grid-template-rows: 1fr .1fr;

    height: 100vh;
}

#Home-Image {
    margin: 0 5em;

    justify-self: center;
    align-self: center;
}

#Home-Content {
    display: flex;
    flex-direction: column;

    align-self: center;

    width: 80%;
}

#Home-Content>.Line-Divider {
    background: linear-gradient(to right, aliceblue 70%, transparent);
    width: 40%;
}

#Home-Scroll {
    grid-column: span 2;
    justify-self: center;
}

/* About Content */

#About {
    display: flex;
    flex-wrap: wrap;

    justify-content: center;
    align-items: center;

    padding: 0 6em;

    height: 70vh;
}

#About>h1 {
    width: 100%;
    text-align: center;
}

#About>div {
    width: 40%;
}

.About-Photo {
    justify-self: center;
}

#About>.About-Text {
    width: 60%;
}

/* Experience content */

#Experience {
    justify-items: center;
    height: 80vh;
}

#Experience > h1 {
    width: 100%;
    text-align: center;
}

#Language-Photos {
    width: 80%;
    height: 40%;

    display: flex;
    flex-wrap: wrap;

    justify-content: space-between;
}

#Experience-Tools {
    width: 80%;
    height: 40%;

    display: flex;
    flex-wrap: wrap;

    justify-content: space-between;
}

#Experience > div > div {
    width: 18%;
}

/* Certifications */
#Certifications {
    height: 60vh;
    width: 80%;

    display: grid;

    grid-template-columns: 1fr 3fr;
    grid-template-rows: .1fr 2fr;

    justify-self: center;

    margin-bottom: 0;
}

#Certifications > h1 {
    grid-column: span 2;
    width: 100%;
    text-align: center;
}

#Certification-Selector > ul{
    height: 100%;
}

#Certification-Selector > ul > li{
    padding: 2% 0;
    transition: .5s;
}

#Certification-Selector > ul > li:hover{
    padding: .9em 0;

    transition: .5s;
}

/* Contact */
#Contact {
    justify-self: center;
    text-align: center;

    margin-top: 0;
}

#Contact > form {
    background-color: #348aa7;

    padding: 1em;
    scale: 1.2;

    border-radius: 4px;

    display: grid;
    grid-template-columns: 1fr 1fr;
}

#Contact > form > p {
    margin-bottom: 0;
    margin-top: .6em;
}

#Contact > form > input {
    margin-bottom: 0;
    margin-top: .6em;
}

#smallText {
    font-size: .5em;
    grid-column: span 2;
}