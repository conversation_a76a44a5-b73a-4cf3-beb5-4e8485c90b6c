/* Colors
bg -  #002b5d
accent 1 - aliceblue; (text, line div)
accent 2 - #348aa7; (anchor links)
*/

@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

body {
    font-family: "Nunito", sans-serif;
    background-color: #002b5d;
    color: aliceblue;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    scroll-behavior: smooth;
}

.Line-Divider {
    background: linear-gradient(to right, transparent, aliceblue, aliceblue, transparent);
    display: block;

    height: 2px;
    margin-bottom: 0;
}

.Main-Content {
    font-size: 1.6em;
    margin: 8em 0;
}

h1 {
    margin-bottom: 1em;
    font-weight: 700;
}

h2 {
    font-size: 2.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

h3 {
    font-size: 1.8em;
    margin: 2em 0 1em 0;
    text-align: center;
    color: #348aa7;
    font-weight: 600;
}

/* Image placeholders */
img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

img[src=""], img:not([src]) {
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-weight: 600;
    text-align: center;
    min-height: 200px;
    position: relative;
}

img[src=""]:after, img:not([src]):after {
    content: attr(alt);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* header */

header {
    display: grid;
    grid-template-columns: 1fr .4fr 1fr;
    grid-template-rows: 1fr .2fr;

    justify-items: center;
    align-items: center;

    position: fixed;
    width: 100%;

    background-color: #002b5d54;
    backdrop-filter: blur(10px);
}

header>h1 {
    margin: 0.4em 1em;
}

header>nav:first-of-type {
    justify-self: end;
}

header>nav:last-of-type {
    justify-self: start;
}

header>nav>a {
    margin: 0 1em;
    color: #348aa7;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5em 1em;
    border-radius: 4px;
}

header>nav>a:hover {
    color: aliceblue;
    background-color: #348aa7;
    transform: translateY(-2px);
}

header>div {
    align-self: normal;
    grid-column: span 3;

    width: 85%;
    transform: translate(0, 10px);
}

/* Home Content */

#Home {
    display: grid;
    grid-template-columns: 2.5fr 2fr;
    grid-template-rows: 1fr .1fr;

    height: 100vh;
}

#Home-Image {
    margin: 0 5em;

    justify-self: center;
    align-self: center;
}

#Home-Content {
    display: flex;
    flex-direction: column;

    align-self: center;

    width: 80%;
}

#Home-Content>.Line-Divider {
    background: linear-gradient(to right, aliceblue 70%, transparent);
    width: 40%;
}

#Home-Scroll {
    grid-column: span 2;
    justify-self: center;
    animation: bounce 2s infinite;
    cursor: pointer;
}

#Home-Scroll p {
    background: linear-gradient(45deg, #348aa7, aliceblue);
    padding: 1em 2em;
    border-radius: 25px;
    margin: 0;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
}

#Home-Scroll:hover p {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.5);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* About Content */

#About {
    display: flex;
    flex-wrap: wrap;

    justify-content: center;
    align-items: center;

    padding: 0 6em;

    height: 70vh;
}

#About>h1 {
    width: 100%;
    text-align: center;
}

#About>div {
    width: 40%;
}

.About-Photo {
    justify-self: center;
    margin: 1em;
}

.About-Photo img {
    max-width: 300px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.About-Photo img:hover {
    transform: scale(1.05);
}

#About>.About-Text {
    width: 60%;
    margin: 1em;
}

#About>.About-Text p {
    font-size: 1.1em;
    text-align: justify;
    margin-bottom: 1.5em;
}

/* Experience content */

#Experience {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    padding: 4em 2em;
}

#Experience > h1 {
    width: 100%;
    text-align: center;
    margin-bottom: 2em;
}

#Language-Photos {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
    margin-bottom: 3em;
}

#Experience-Tools {
    width: 80%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2em;
}

#Language-Photos > div,
#Experience-Tools > div {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(52, 138, 167, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

#Language-Photos > div:hover,
#Experience-Tools > div:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 12px 30px rgba(52, 138, 167, 0.5);
}

#Language-Photos img,
#Experience-Tools img {
    max-width: 80px;
    max-height: 80px;
    filter: brightness(1.1);
}

/* Certifications */
#Certifications {
    min-height: 60vh;
    width: 90%;
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: auto 1fr;
    gap: 2em;
    justify-self: center;
    margin: 4em auto;
    padding: 2em;
}

#Certifications > h1 {
    grid-column: span 2;
    width: 100%;
    text-align: center;
    margin-bottom: 1em;
}

#Certification-Selector {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 1.5em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#Certification-Selector > ul {
    list-style: none;
    padding: 0;
    margin: 0;
    height: 100%;
}

#Certification-Selector > ul > li {
    padding: 1em 1.5em;
    margin-bottom: 0.5em;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 138, 167, 0.3);
}

#Certification-Selector > ul > li:hover {
    transform: translateX(10px);
    background: linear-gradient(135deg, #5ba3c7, #348aa7);
    box-shadow: 0 4px 15px rgba(52, 138, 167, 0.5);
}

#Certs {
    background: rgba(52, 138, 167, 0.1);
    border-radius: 15px;
    padding: 2em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

#Certs > div {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #348aa7, #5ba3c7);
    border: 2px dashed aliceblue;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: aliceblue;
    font-size: 1.2em;
    font-weight: 600;
    text-align: center;
}

#Certs > div:after {
    content: "Certificate Display Area\A(Click certification to view)";
    white-space: pre-line;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Contact */
#Contact {
    justify-self: center;
    text-align: center;

    margin-top: 0;
}

#Contact > form {
    background-color: #348aa7;

    padding: 1em;
    scale: 1.2;

    border-radius: 4px;

    display: grid;
    grid-template-columns: 1fr 1fr;
}

#Contact > form > p {
    margin-bottom: 0;
    margin-top: .6em;
}

#Contact > form > input {
    margin-bottom: 0;
    margin-top: .6em;
}

#smallText {
    font-size: .5em;
    grid-column: span 2;
}